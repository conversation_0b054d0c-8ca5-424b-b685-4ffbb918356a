---
import { Image } from "astro:assets";
import type { SoftwareScreen } from "@/data/portfolio";
import { sectionTitles } from "@/data/portfolio";
import softwareScreenshot from "@/assets/images/software-screenshot.svg";

interface Props {
    softwareScreens: SoftwareScreen[];
}

const { softwareScreens } = Astro.props;
---

<section id="screens" class="py-10 border-t border-slate-200">
    <h2 class="text-2xl text-sky-900 font-extrabold">{sectionTitles.softwareScreens}</h2>
    <p class="mt-2 text-sm text-slate-600">Placeholders for statistical software interfaces (Stata, SPSS).</p>
    <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
        {
            softwareScreens.map((screen) => (
                <figure class="bg-white border border-slate-200 rounded p-2">
                    <Image src={softwareScreenshot} alt={screen.alt} width={800} height={480} class="w-full h-auto" />
                    <figcaption class="text-xs text-slate-600 mt-2">{screen.description}</figcaption>
                </figure>
            ))
        }
    </div>
</section>
