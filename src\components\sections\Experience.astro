---
import type { Experience, Education, Certification } from "@/data/portfolio";
import { sectionTitles } from "@/data/portfolio";

interface Props {
    employment: Experience[];
    education: Education[];
    certifications: Certification[];
}

const { employment, education, certifications } = Astro.props;
---

<section id="experience" aria-labelledby="exp-title" class="py-10 border-t border-slate-200">
    <h2 id="exp-title" class="text-2xl text-sky-900 font-extrabold">{sectionTitles.professionalExperience}</h2>

    <div class="mt-6 space-y-8">
        <!-- Employment Experience -->
        {
            employment.length > 0 && (
                <section aria-labelledby="employment-title">
                    <h3 id="employment-title" class="text-sky-900 font-bold">
                        {sectionTitles.employmentExperience}
                    </h3>
                    <div class="mt-3 grid gap-4">
                        {employment.map((job) => (
                            <article class="bg-white border border-slate-200 rounded p-4">
                                <h4 class="text-sky-900 font-semibold">{job.title}</h4>
                                <p class="text-sm text-slate-600">
                                    {job.organization} — {job.period}
                                </p>
                                <ul class="list-disc pl-5 mt-2 text-sm space-y-1">
                                    {job.responsibilities.map((responsibility) => (
                                        <li>{responsibility}</li>
                                    ))}
                                </ul>
                            </article>
                        ))}
                    </div>
                </section>
            )
        }

        <!-- Academic Qualifications -->
        {
            education.length > 0 && (
                <section aria-labelledby="degrees-title" class="border-t border-slate-100 pt-6">
                    <h3 id="degrees-title" class="text-sky-900 font-bold">
                        {sectionTitles.academicQualifications}
                    </h3>
                    <div class="mt-3 grid gap-4">
                        {education.map((degree) => (
                            <article class="bg-white border border-slate-200 rounded p-4">
                                <h4 class="text-sky-900 font-semibold">{degree.degree}</h4>
                                <p class="text-sm text-slate-600">
                                    {degree.institution} — {degree.period}
                                </p>
                                {degree.details && degree.details.length > 0 && (
                                    <ul class="list-disc pl-5 mt-2 text-sm space-y-1">
                                        {degree.details.map((detail) => (
                                            <li>{detail}</li>
                                        ))}
                                    </ul>
                                )}
                            </article>
                        ))}
                    </div>
                </section>
            )
        }

        <!-- Professional Certifications -->
        {
            certifications.length > 0 && (
                <section aria-labelledby="certs-title" class="border-t border-slate-100 pt-6">
                    <h3 id="certs-title" class="text-sky-900 font-bold">
                        {sectionTitles.professionalCertifications}
                    </h3>
                    <div class="mt-3 grid gap-4">
                        {certifications.map((cert) => (
                            <article class="bg-white border border-slate-200 rounded p-4">
                                <h4 class="text-sky-900 font-semibold">{cert.title}</h4>
                                <p class="text-sm text-slate-600">
                                    {cert.issuer} — {cert.issued}
                                </p>
                            </article>
                        ))}
                    </div>
                </section>
            )
        }
    </div>
</section>
