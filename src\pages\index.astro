---
import Layout from "@/layouts/main.astro";
import Header from "@/components/layout/Header.astro";
import Hero from "@/components/sections/Hero.astro";
import PublicationsWithFilter from "@/components/publications/PublicationsWithFilter.vue";
import Experience from "@/components/sections/Experience.astro";
import SoftwareScreens from "@/components/sections/SoftwareScreens.astro";
import Footer from "@/components/layout/Footer.astro";
import {
    personalInfo,
    publications,
    employment,
    education,
    certifications,
    softwareScreens,
    navigation,
    siteInfo,
} from "@/data/portfolio";
---

<Layout title={siteInfo.title} bodyClass="bg-[#f3f6fb] text-slate-800">
    <Header brandName={siteInfo.brandName} navigation={navigation} skipToContentText={siteInfo.skipToContentText} />

    <main id="main" class="mx-auto max-w-6xl px-4 sm:px-6">
        <Hero personalInfo={personalInfo} />

        <PublicationsWithFilter publications={publications} client:load />

        <Experience employment={employment} education={education} certifications={certifications} />

        <SoftwareScreens softwareScreens={softwareScreens} />
    </main>

    <Footer personalName={personalInfo.name} footerText={siteInfo.footerText} />
</Layout>
